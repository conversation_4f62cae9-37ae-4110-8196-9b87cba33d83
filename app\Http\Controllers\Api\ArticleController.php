<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ArticleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Article::with(['author', 'commentaires.user'])
            ->published()
            ->recent();

        // Filtrage par catégorie si spécifié
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Recherche par titre
        if ($request->has('search')) {
            $query->where('titre', 'like', '%' . $request->search . '%');
        }

        $articles = $query->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => $articles->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->titre,
                    'slug' => $article->slug,
                    'excerpt' => $article->excerpt,
                    'content' => $article->message,
                    'image' => $article->image_url,
                    'video' => $article->video_url,
                    'author' => $article->author ? $article->author->full_name : 'Auteur inconnu',
                    'date' => $article->date_publication->format('Y-m-d'),
                    'time' => $article->heure_publication->format('H:i'),
                    'comments_count' => $article->commentaires->count(),
                    'created_at' => $article->created_at,
                ];
            }),
            'pagination' => [
                'current_page' => $articles->currentPage(),
                'last_page' => $articles->lastPage(),
                'per_page' => $articles->perPage(),
                'total' => $articles->total(),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'titre' => 'required|string|max:255',
            'message' => 'required|string',
            'author_id' => 'required|exists:authors,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video' => 'nullable|mimes:mp4,avi,mov|max:10240',
            'date_publication' => 'required|date',
            'heure_publication' => 'required|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Upload de l'image
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('articles/images', 'public');
        }

        // Upload de la vidéo
        if ($request->hasFile('video')) {
            $data['video'] = $request->file('video')->store('articles/videos', 'public');
        }

        $article = Article::create($data);
        $article->load(['author', 'commentaires.user']);

        return response()->json([
            'success' => true,
            'message' => 'Article créé avec succès',
            'data' => $article
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $slug): JsonResponse
    {
        $article = Article::with(['author', 'commentaires.user'])
            ->where('titre', 'like', '%' . str_replace('-', ' ', $slug) . '%')
            ->orWhere('id', $slug)
            ->first();

        if (!$article) {
            return response()->json([
                'success' => false,
                'message' => 'Article non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $article->id,
                'title' => $article->titre,
                'slug' => $article->slug,
                'content' => $article->message,
                'image' => $article->image_url,
                'video' => $article->video_url,
                'author' => $article->author ? $article->author->full_name : 'Auteur inconnu',
                'date' => $article->date_publication->format('Y-m-d'),
                'time' => $article->heure_publication->format('H:i'),
                'comments' => $article->commentaires->map(function ($comment) {
                    return [
                        'id' => $comment->id,
                        'message' => $comment->message,
                        'author' => $comment->user->nom,
                        'date' => $comment->date_commentaire->format('Y-m-d'),
                        'time' => $comment->heure_commentaire->format('H:i'),
                    ];
                }),
                'created_at' => $article->created_at,
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $article = Article::find($id);

        if (!$article) {
            return response()->json([
                'success' => false,
                'message' => 'Article non trouvé'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'titre' => 'sometimes|required|string|max:255',
            'message' => 'sometimes|required|string',
            'author_id' => 'sometimes|required|exists:authors,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video' => 'nullable|mimes:mp4,avi,mov|max:10240',
            'date_publication' => 'sometimes|required|date',
            'heure_publication' => 'sometimes|required|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Upload de l'image
        if ($request->hasFile('image')) {
            // Supprimer l'ancienne image
            if ($article->image) {
                Storage::disk('public')->delete($article->image);
            }
            $data['image'] = $request->file('image')->store('articles/images', 'public');
        }

        // Upload de la vidéo
        if ($request->hasFile('video')) {
            // Supprimer l'ancienne vidéo
            if ($article->video) {
                Storage::disk('public')->delete($article->video);
            }
            $data['video'] = $request->file('video')->store('articles/videos', 'public');
        }

        $article->update($data);
        $article->load(['author', 'commentaires.user']);

        return response()->json([
            'success' => true,
            'message' => 'Article mis à jour avec succès',
            'data' => $article
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $article = Article::find($id);

        if (!$article) {
            return response()->json([
                'success' => false,
                'message' => 'Article non trouvé'
            ], 404);
        }

        // Supprimer les fichiers associés
        if ($article->image) {
            Storage::disk('public')->delete($article->image);
        }
        if ($article->video) {
            Storage::disk('public')->delete($article->video);
        }

        $article->delete();

        return response()->json([
            'success' => true,
            'message' => 'Article supprimé avec succès'
        ]);
    }
}
