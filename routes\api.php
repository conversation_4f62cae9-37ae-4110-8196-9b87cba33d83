<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ArticleController;
use App\Http\Controllers\Api\CommentaireController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\AuthorController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques
Route::prefix('auth')->group(function () {
    Route::post('admin/login', [AuthController::class, 'adminLogin']);
    Route::post('author/login', [AuthController::class, 'authorLogin']);
    Route::post('user/register', [AuthController::class, 'userRegister']);
    Route::post('user/login', [AuthController::class, 'userLogin']);
});

// Routes publiques pour les articles
Route::get('articles', [ArticleController::class, 'index']);
Route::get('articles/{slug}', [ArticleController::class, 'show']);

// Routes protégées
Route::middleware('auth:sanctum')->group(function () {
    // Routes d'authentification
    Route::post('auth/logout', [AuthController::class, 'logout']);
    Route::get('auth/me', [AuthController::class, 'me']);
    
    // Routes pour les commentaires (utilisateurs connectés)
    Route::apiResource('commentaires', CommentaireController::class);
    
    // Routes admin/auteur pour les articles
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('articles', [ArticleController::class, 'store']);
        Route::put('articles/{id}', [ArticleController::class, 'update']);
        Route::delete('articles/{id}', [ArticleController::class, 'destroy']);
    });
    
    // Routes admin uniquement
    Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
        Route::apiResource('authors', AuthorController::class);
        Route::apiResource('admins', AdminController::class);
    });
});

// Route pour obtenir les informations de l'utilisateur connecté
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
