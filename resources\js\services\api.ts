import axios from 'axios';

// Configuration de base d'Axios
const api = axios.create({
  baseURL: '/api/v1',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      window.location.href = '/admin/login';
    }
    return Promise.reject(error);
  }
);

// Types pour l'API
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  bio?: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  is_active: boolean;
  sort_order: number;
  articles_count?: number;
}

export interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image?: string;
  author: string;
  category_id: number;
  category?: Category;
  user_id: number;
  user?: User;
  is_published: boolean;
  is_featured: boolean;
  published_at?: string;
  views_count: number;
  tags?: string[];
  meta_data?: any;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: number;
  article_id: number;
  author_name: string;
  author_email: string;
  author_website?: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected';
  parent_id?: number;
  replies?: Comment[];
  created_at: string;
}

export interface Subscriber {
  id: number;
  email: string;
  name?: string;
  is_active: boolean;
  subscribed_at: string;
  unsubscribed_at?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// Services API
export const authAPI = {
  login: (credentials: LoginCredentials) => 
    api.post<AuthResponse>('/auth/login', credentials),
  
  logout: () => 
    api.post('/auth/logout'),
  
  me: () => 
    api.get<{ user: User }>('/auth/me'),
  
  updateProfile: (data: Partial<User>) => 
    api.put<{ user: User; message: string }>('/auth/profile', data),
  
  changePassword: (data: { current_password: string; password: string; password_confirmation: string }) => 
    api.put<{ message: string }>('/auth/password', data),
};

export const articlesAPI = {
  getAll: (params?: any) => 
    api.get<any>('/articles', { params }),
  
  getById: (id: string | number) => 
    api.get<Article>(`/articles/${id}`),
  
  create: (data: Partial<Article>) => 
    api.post<{ article: Article; message: string }>('/articles', data),
  
  update: (id: number, data: Partial<Article>) => 
    api.put<{ article: Article; message: string }>(`/articles/${id}`, data),
  
  delete: (id: number) => 
    api.delete<{ message: string }>(`/articles/${id}`),
};

export const categoriesAPI = {
  getAll: (params?: any) => 
    api.get<Category[]>('/categories', { params }),
  
  getById: (id: string | number) => 
    api.get<{ category: Category; articles: any }>(`/categories/${id}`),
  
  create: (data: Partial<Category>) => 
    api.post<{ category: Category; message: string }>('/categories', data),
  
  update: (id: number, data: Partial<Category>) => 
    api.put<{ category: Category; message: string }>(`/categories/${id}`, data),
  
  delete: (id: number) => 
    api.delete<{ message: string }>(`/categories/${id}`),
  
  reorder: (categories: { id: number; sort_order: number }[]) => 
    api.post<{ message: string }>('/categories/reorder', { categories }),
};

export const commentsAPI = {
  getByArticle: (articleId: number) => 
    api.get<Comment[]>(`/articles/${articleId}/comments`),
  
  create: (articleId: number, data: Partial<Comment>) => 
    api.post<{ comment: Comment; message: string }>(`/articles/${articleId}/comments`, data),
  
  update: (id: number, data: Partial<Comment>) => 
    api.put<{ comment: Comment; message: string }>(`/comments/${id}`, data),
  
  delete: (id: number) => 
    api.delete<{ message: string }>(`/comments/${id}`),
  
  approve: (id: number) => 
    api.put<{ message: string }>(`/comments/${id}/approve`),
  
  reject: (id: number) => 
    api.put<{ message: string }>(`/comments/${id}/reject`),
};

export const subscribersAPI = {
  getAll: (params?: any) => 
    api.get<any>('/subscribers', { params }),
  
  subscribe: (data: { email: string; name?: string }) => 
    api.post<{ message: string }>('/newsletter/subscribe', data),
  
  unsubscribe: (token: string) => 
    api.post<{ message: string }>('/newsletter/unsubscribe', { token }),
  
  delete: (id: number) => 
    api.delete<{ message: string }>(`/subscribers/${id}`),
};

export default api;
