import React from 'react';
import { 
  DocumentTextIcon, 
  ChatBubbleLeftRightIcon, 
  UsersIcon, 
  PhotoIcon 
} from '@heroicons/react/24/outline';
import StatsCard from '../../components/admin/Dashboard/StatsCard';
import ArticlesChart from '../../components/admin/Dashboard/ArticlesChart';
import CategoriesChart from '../../components/admin/Dashboard/CategoriesChart';
import adminData from '../../data/adminData.json';

const DashboardPage: React.FC = () => {
  const { stats, chartData } = adminData;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
        <p className="text-gray-600">Vue d'ensemble de votre journal numérique</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Articles"
          value={stats.totalArticles}
          icon={DocumentTextIcon}
          color="bg-blue-500"
          trend={{ value: 12, isPositive: true }}
        />
        <StatsCard
          title="Commentaires"
          value={stats.totalComments}
          icon={ChatBubbleLeftRightIcon}
          color="bg-green-500"
          trend={{ value: 8, isPositive: true }}
        />
        <StatsCard
          title="Abonnés"
          value={stats.totalSubscribers}
          icon={UsersIcon}
          color="bg-purple-500"
          trend={{ value: 15, isPositive: true }}
        />
        <StatsCard
          title="Médias"
          value={stats.totalMedias}
          icon={PhotoIcon}
          color="bg-orange-500"
          trend={{ value: 5, isPositive: true }}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ArticlesChart data={chartData.articlesPerMonth} />
        <CategoriesChart data={chartData.categoriesStats} />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Articles</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Publiés</span>
              <span className="font-semibold text-green-600">{stats.publishedArticles}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Brouillons</span>
              <span className="font-semibold text-yellow-600">{stats.draftArticles}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Commentaires</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Approuvés</span>
              <span className="font-semibold text-green-600">{stats.approvedComments}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">En attente</span>
              <span className="font-semibold text-orange-600">{stats.pendingComments}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
          <div className="space-y-3 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Nouvel article publié</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">5 nouveaux commentaires</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-gray-600">12 nouveaux abonnés</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;