import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '../services/api';
import type { User, Admin, Author } from '../types';

// -------------------- Types --------------------
interface AdminContextType {
  user: User | Admin | Author | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User | Admin | Author>) => void;
}

interface AdminProviderProps {
  children: ReactNode;
}

// -------------------- Context --------------------
const AdminContext = createContext<AdminContextType | undefined>(undefined);

export const useAdmin = (): AdminContextType => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

// -------------------- Provider --------------------
export const AdminProvider: React.FC<AdminProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | Admin | Author | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Vérifier l’authentification au chargement
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('auth_token');
      const savedUser = localStorage.getItem('auth_user');

      if (token && savedUser) {
        try {
          await authAPI.me(); // Vérifie si le token est valide
          const userData = JSON.parse(savedUser);
          setUser(userData);
          setIsAuthenticated(true);
        } catch {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Connexion
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await authAPI.adminLogin({ email, password });
      const data = response.data;
      if (data.success) {
        const loggedUser = data.data.admin || data.data.author || data.data.user;
        localStorage.setItem('auth_token', data.data.token);
        localStorage.setItem('auth_user', JSON.stringify(loggedUser));

        setUser(loggedUser);
        setIsAuthenticated(true);
        return true;
      }
      return false;
    } catch (err) {
      console.error('Erreur de connexion:', err);
      return false;
    }
  };

  // Déconnexion
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (err) {
      console.error('Erreur lors de la déconnexion:', err);
    } finally {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  // Mettre à jour les informations utilisateur
  const updateUser = (userData: Partial<User | Admin | Author>) => {
    if (user) {
      const updated = { ...user, ...userData };
      setUser(updated);
      localStorage.setItem('auth_user', JSON.stringify(updated));
    }
  };

  return (
    <AdminContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        updateUser,
      }}
    >
      {children}
    </AdminContext.Provider>
  );
};
