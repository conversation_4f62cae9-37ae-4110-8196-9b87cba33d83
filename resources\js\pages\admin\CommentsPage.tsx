import React, { useState } from 'react';
import { CheckIcon, XMarkIcon, TrashIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import adminData from '../../data/adminData.json';
import type { AdminComment } from '../../types/admin';

const CommentsPage: React.FC = () => {
  const [comments, setComments] = useState<AdminComment[]>(adminData.comments);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const handleApprove = (id: number) => {
    setComments(prev => prev.map(comment => 
      comment.id === id ? { ...comment, status: 'approved' as const } : comment
    ));
  };

  const handleReject = (id: number) => {
    setComments(prev => prev.map(comment => 
      comment.id === id ? { ...comment, status: 'rejected' as const } : comment
    ));
  };

  const handleDelete = (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce commentaire ?')) {
      setComments(prev => prev.filter(comment => comment.id !== id));
    }
  };

  const filteredComments = comments.filter(comment => 
    statusFilter === 'all' || comment.status === statusFilter
  );

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'Approuvé';
      case 'pending': return 'En attente';
      case 'rejected': return 'Rejeté';
      default: return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Commentaires</h1>
        <p className="text-gray-600">Modérez les commentaires de vos articles</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-gray-900">{comments.length}</div>
          <div className="text-sm text-gray-600">Total commentaires</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-green-600">
            {comments.filter(c => c.status === 'approved').length}
          </div>
          <div className="text-sm text-gray-600">Approuvés</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-yellow-600">
            {comments.filter(c => c.status === 'pending').length}
          </div>
          <div className="text-sm text-gray-600">En attente</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-red-600">
            {comments.filter(c => c.status === 'rejected').length}
          </div>
          <div className="text-sm text-gray-600">Rejetés</div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Filtrer par statut :</label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="approved">Approuvés</option>
            <option value="rejected">Rejetés</option>
          </select>
        </div>
      </div>

      {/* Comments List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="divide-y divide-gray-200">
          {filteredComments.map((comment) => (
            <div key={comment.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-sm font-medium text-gray-900">{comment.author}</h3>
                    <span className="text-sm text-gray-500">{comment.email}</span>
                    <span className={getStatusBadge(comment.status)}>
                      {getStatusText(comment.status)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    Article: <span className="font-medium">{comment.articleTitle}</span>
                  </p>
                  
                  <p className="text-gray-800 mb-3">{comment.content}</p>
                  
                  <p className="text-xs text-gray-500">
                    {format(new Date(comment.date), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                  </p>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {comment.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleApprove(comment.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="Approuver"
                      >
                        <CheckIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleReject(comment.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Rejeter"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  <button
                    onClick={() => handleDelete(comment.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Supprimer"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredComments.length === 0 && (
          <div className="p-12 text-center">
            <p className="text-gray-500">Aucun commentaire trouvé pour ce filtre.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentsPage;