<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'author_id',
        'titre',
        'message',
        'image',
        'video',
        'date_publication',
        'heure_publication',
    ];

    protected $casts = [
        'date_publication' => 'date',
        'heure_publication' => 'datetime',
    ];

    /**
     * Relation avec l'auteur
     */
    public function author()
    {
        return $this->belongsTo(Author::class);
    }

    /**
     * Relation avec les commentaires
     */
    public function commentaires()
    {
        return $this->hasMany(Commentaire::class);
    }

    /**
     * Génère automatiquement un slug basé sur le titre
     */
    public function getSlugAttribute()
    {
        return Str::slug($this->titre);
    }

    /**
     * Retourne l'URL complète de l'image
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return asset('storage/' . $this->image);
        }
        return null;
    }

    /**
     * Retourne l'URL complète de la vidéo
     */
    public function getVideoUrlAttribute()
    {
        if ($this->video) {
            return asset('storage/' . $this->video);
        }
        return null;
    }

    /**
     * Scope pour les articles publiés
     */
    public function scopePublished($query)
    {
        return $query->where('date_publication', '<=', now()->toDateString());
    }

    /**
     * Scope pour les articles récents
     */
    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('date_publication', 'desc')
                    ->orderBy('heure_publication', 'desc')
                    ->limit($limit);
    }

    /**
     * Retourne un extrait du message
     */
    public function getExcerptAttribute($length = 150)
    {
        return Str::limit(strip_tags($this->message), $length);
    }
}
