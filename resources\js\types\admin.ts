export interface AdminStats {
  totalArticles: number;
  totalComments: number;
  totalSubscribers: number;
  totalMedias: number;
  publishedArticles: number;
  draftArticles: number;
  pendingComments: number;
  approvedComments: number;
}

export interface ChartDataPoint {
  month: string;
  articles: number;
  views: number;
}

export interface CategoryStat {
  name: string;
  count: number;
  color: string;
}

export interface AdminArticle {
  id: number;
  title: string;
  category: string;
  author: string;
  status: 'published' | 'draft';
  date: string;
  views: number;
  comments: number;
}

export interface AdminComment {
  id: number;
  articleTitle: string;
  author: string;
  email: string;
  content: string;
  date: string;
  status: 'approved' | 'pending' | 'rejected';
}

export interface Subscriber {
  id: number;
  email: string;
  subscriptionDate: string;
  status: 'active' | 'inactive';
}

export interface Media {
  id: number;
  name: string;
  type: 'image' | 'video' | 'audio';
  size: string;
  url: string;
  uploadDate: string;
}

export interface ArticleFormData {
  title: string;
  excerpt: string;
  content: string;
  category: string;
  author: string;
  status: 'published' | 'draft';
  tags: string[];
  featuredImage?: string;
  videoUrl?: string;
}