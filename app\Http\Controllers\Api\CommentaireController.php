<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Commentaire;
use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CommentaireController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Commentaire::with(['user', 'article'])
            ->recent();

        // Filtrage par article si spécifié
        if ($request->has('article_id')) {
            $query->forArticle($request->article_id);
        }

        $commentaires = $query->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => $commentaires->map(function ($commentaire) {
                return [
                    'id' => $commentaire->id,
                    'message' => $commentaire->message,
                    'author' => $commentaire->user->nom,
                    'article_title' => $commentaire->article->titre,
                    'date' => $commentaire->date_commentaire->format('Y-m-d'),
                    'time' => $commentaire->heure_commentaire->format('H:i'),
                    'created_at' => $commentaire->created_at,
                ];
            }),
            'pagination' => [
                'current_page' => $commentaires->currentPage(),
                'last_page' => $commentaires->lastPage(),
                'per_page' => $commentaires->perPage(),
                'total' => $commentaires->total(),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Vérifier que l'article existe
        $article = Article::find($request->article_id);
        if (!$article) {
            return response()->json([
                'success' => false,
                'message' => 'Article non trouvé'
            ], 404);
        }

        $commentaire = Commentaire::create([
            'article_id' => $request->article_id,
            'user_id' => $request->user()->id,
            'message' => $request->message,
            'date_commentaire' => now()->toDateString(),
            'heure_commentaire' => now()->toTimeString(),
        ]);

        $commentaire->load(['user', 'article']);

        return response()->json([
            'success' => true,
            'message' => 'Commentaire ajouté avec succès',
            'data' => [
                'id' => $commentaire->id,
                'message' => $commentaire->message,
                'author' => $commentaire->user->nom,
                'date' => $commentaire->date_commentaire->format('Y-m-d'),
                'time' => $commentaire->heure_commentaire->format('H:i'),
                'created_at' => $commentaire->created_at,
            ]
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $commentaire = Commentaire::with(['user', 'article'])->find($id);

        if (!$commentaire) {
            return response()->json([
                'success' => false,
                'message' => 'Commentaire non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $commentaire->id,
                'message' => $commentaire->message,
                'author' => $commentaire->user->nom,
                'article_title' => $commentaire->article->titre,
                'date' => $commentaire->date_commentaire->format('Y-m-d'),
                'time' => $commentaire->heure_commentaire->format('H:i'),
                'created_at' => $commentaire->created_at,
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $commentaire = Commentaire::find($id);

        if (!$commentaire) {
            return response()->json([
                'success' => false,
                'message' => 'Commentaire non trouvé'
            ], 404);
        }

        // Vérifier que l'utilisateur peut modifier ce commentaire
        if ($commentaire->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Non autorisé à modifier ce commentaire'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $commentaire->update([
            'message' => $request->message,
        ]);

        $commentaire->load(['user', 'article']);

        return response()->json([
            'success' => true,
            'message' => 'Commentaire mis à jour avec succès',
            'data' => [
                'id' => $commentaire->id,
                'message' => $commentaire->message,
                'author' => $commentaire->user->nom,
                'date' => $commentaire->date_commentaire->format('Y-m-d'),
                'time' => $commentaire->heure_commentaire->format('H:i'),
                'created_at' => $commentaire->created_at,
            ]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        $commentaire = Commentaire::find($id);

        if (!$commentaire) {
            return response()->json([
                'success' => false,
                'message' => 'Commentaire non trouvé'
            ], 404);
        }

        // Vérifier que l'utilisateur peut supprimer ce commentaire
        if ($commentaire->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Non autorisé à supprimer ce commentaire'
            ], 403);
        }

        $commentaire->delete();

        return response()->json([
            'success' => true,
            'message' => 'Commentaire supprimé avec succès'
        ]);
    }
}
