import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  HomeIcon, 
  DocumentTextIcon, 
  FolderIcon, 
  PhotoIcon, 
  ChatBubbleLeftRightIcon, 
  UsersIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { useAdmin } from '../../../contexts/AdminContext';

const AdminSidebar: React.FC = () => {
  const location = useLocation();
  const { logout } = useAdmin();

  const navigation = [
    { name: 'Tableau de bord', href: '/admin/dashboard', icon: HomeIcon },
    { name: 'Articles', href: '/admin/articles', icon: DocumentTextIcon },
    { name: 'Catégories', href: '/admin/categories', icon: FolderIcon },
    { name: 'Médias', href: '/admin/medias', icon: PhotoIcon },
    { name: 'Commentaires', href: '/admin/comments', icon: ChatBubbleLeftRightIcon },
    { name: 'Abonn<PERSON>', href: '/admin/subscribers', icon: UsersIcon },
    { name: 'Statistiques', href: '/admin/stats', icon: ChartBarIcon },
    { name: 'Paramètres', href: '/admin/settings', icon: Cog6ToothIcon },
  ];

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  return (
    <div className="flex flex-col w-64 bg-gray-900 min-h-screen">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 bg-gray-800">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">FZ</span>
          </div>
          <span className="text-white font-bold text-lg">Admin</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                isActive(item.href)
                  ? 'bg-green-600 text-white'
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
              }`}
            >
              <Icon className="w-5 h-5 mr-3" />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Logout */}
      <div className="p-4 border-t border-gray-800">
        <button
          onClick={logout}
          className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition-colors"
        >
          <ArrowRightOnRectangleIcon className="w-5 h-5 mr-3" />
          Déconnexion
        </button>
      </div>
    </div>
  );
};

export default AdminSidebar;