export interface Article {
  id: number;
  title: string;
  slug: string;
  category: string;
  excerpt: string;
  content: string;
  image: string;
  author: string;
  date: string;
  readTime: number;
  tags: string[];
  videoUrl?: string;
  featured: boolean;
}

export interface Category {
  name: string;
  slug: string;
  color: string;
}

export interface Comment {
  id: number;
  articleId: number;
  author: string;
  content: string;
  date: string;
  replies?: Comment[];
}