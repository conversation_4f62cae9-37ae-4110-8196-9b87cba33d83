export interface Article {
  id: number;
  title: string;
  slug: string;
  category?: string;
  excerpt: string;
  content: string;
  image: string;
  author: string;
  author_id?: number;
  date: string;
  time?: string;
  readTime?: number;
  tags?: string[];
  videoUrl?: string;
  video?: string;
  featured?: boolean;
  comments_count?: number;
  created_at?: string;
}

export interface Category {
  name: string;
  slug: string;
  color: string;
}

export interface Comment {
  id: number;
  articleId: number;
  article_id: number;
  author: string;
  content: string;
  message: string;
  date: string;
  time?: string;
  user_id?: number;
  replies?: Comment[];
}

export interface User {
  id: number;
  nom: string;
  email: string;
  type?: 'admin' | 'author' | 'user';
}

export interface Admin {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone?: string;
  full_name: string;
}

export interface Author {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone?: string;
  admin_id: number;
  full_name: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user?: User;
    admin?: Admin;
    author?: Author;
    token: string;
    type: 'admin' | 'author' | 'user';
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  pagination?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}