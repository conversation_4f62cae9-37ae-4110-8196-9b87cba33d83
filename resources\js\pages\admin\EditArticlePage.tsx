import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ArticleForm from '../../components/admin/Articles/ArticleForm';
import type { ArticleFormData } from '../../types/admin';

const EditArticlePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Simulation des données existantes
  const existingData: ArticleFormData = {
    title: 'PSG vs Manchester City : Le Choc des Titans en Ligue des Champions',
    excerpt: 'Le Paris Saint-Germain affronte Manchester City dans un match décisif pour la qualification en quarts de finale.',
    content: '<p>Le Parc des Princes s\'apprête à vivre une soirée européenne exceptionnelle ce soir...</p>',
    category: 'Actualités',
    author: '<PERSON>',
    status: 'published',
    tags: ['PSG', 'Manchester City', 'Ligue des Champions'],
    featuredImage: 'https://images.pexels.com/photos/274506/pexels-photo-274506.jpeg',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
  };

  const handleSubmit = (data: ArticleFormData) => {
    // Ici, vous feriez un appel API pour mettre à jour l'article
    console.log('Mise à jour de l\'article:', id, data);
    
    // Simulation de la mise à jour
    setTimeout(() => {
      alert('Article mis à jour avec succès !');
      navigate('/admin/articles');
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Modifier l'article</h1>
        <p className="text-gray-600">Modifiez les informations de votre article</p>
      </div>

      {/* Form */}
      <ArticleForm 
        initialData={existingData} 
        onSubmit={handleSubmit} 
        isEditing={true} 
      />
    </div>
  );
};

export default EditArticlePage;