<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API FootballZone</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test API FootballZone</h1>
    
    <div class="test-section">
        <h2>1. Test de connexion Admin</h2>
        <button onclick="testAdminLogin()">Tester la connexion admin</button>
        <div id="admin-login-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test récupération des articles</h2>
        <button onclick="testGetArticles()">Récupérer les articles</button>
        <div id="articles-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test récupération des commentaires</h2>
        <button onclick="testGetComments()">Récupérer les commentaires</button>
        <div id="comments-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test récupération des auteurs (nécessite connexion admin)</h2>
        <button onclick="testGetAuthors()">Récupérer les auteurs</button>
        <div id="authors-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;

        async function makeRequest(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(API_BASE + url, {
                    ...options,
                    headers
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const className = result.success ? 'success' : 'error';
            element.className = `test-section ${className}`;
            element.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }

        async function testAdminLogin() {
            const result = await makeRequest('/auth/admin/login', {
                method: 'POST',
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'admin123'
                })
            });

            if (result.success && result.data.success) {
                authToken = result.data.data.token;
                console.log('Token sauvegardé:', authToken);
            }

            displayResult('admin-login-result', result);
        }

        async function testGetArticles() {
            const result = await makeRequest('/articles');
            displayResult('articles-result', result);
        }

        async function testGetComments() {
            const result = await makeRequest('/commentaires');
            displayResult('comments-result', result);
        }

        async function testGetAuthors() {
            if (!authToken) {
                displayResult('authors-result', {
                    success: false,
                    error: 'Vous devez d\'abord vous connecter en tant qu\'admin'
                });
                return;
            }

            const result = await makeRequest('/admin/authors');
            displayResult('authors-result', result);
        }
    </script>
</body>
</html>
