import React, { useState } from 'react';
import { MessageCircle, User, Send, Flag } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Comment {
  id: number;
  author: string;
  content: string;
  date: string;
  replies?: Comment[];
}

interface CommentSectionProps {
  articleId: number;
}

const CommentSection: React.FC<CommentSectionProps> = ({ articleId }) => {
  const [comments, setComments] = useState<Comment[]>([
    {
      id: 1,
      author: "<PERSON>",
      content: "Excellente analyse ! J'espère que Mbappé va rester au PSG encore quelques années.",
      date: "2024-03-15T10:30:00Z",
      replies: [
        {
          id: 2,
          author: "<PERSON>",
          content: "Je pense au contraire qu'il est temps pour lui de découvrir un nouveau championnat.",
          date: "2024-03-15T11:15:00Z"
        }
      ]
    },
    {
      id: 3,
      author: "<PERSON>",
      content: "Le match va être épique ! Vivement ce soir pour voir ça.",
      date: "2024-03-15T09:45:00Z"
    }
  ]);

  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);

  const handleSubmitComment = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now(),
      author: "Vous",
      content: newComment,
      date: new Date().toISOString()
    };

    if (replyingTo) {
      setComments(prev => prev.map(c => {
        if (c.id === replyingTo) {
          return {
            ...c,
            replies: [...(c.replies || []), comment]
          };
        }
        return c;
      }));
    } else {
      setComments(prev => [comment, ...prev]);
    }

    setNewComment('');
    setReplyingTo(null);
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment.id} className={`${isReply ? 'ml-8 border-l-2 border-gray-200 pl-4' : ''}`}>
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-semibold text-gray-900">{comment.author}</span>
              <span className="text-gray-500 text-sm">
                {format(new Date(comment.date), 'dd MMM yyyy à HH:mm', { locale: fr })}
              </span>
            </div>
            <p className="text-gray-700 mb-3">{comment.content}</p>
            <div className="flex items-center space-x-4 text-sm">
              {!isReply && (
                <button
                  onClick={() => setReplyingTo(comment.id)}
                  className="text-green-600 hover:text-green-700 flex items-center"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  Répondre
                </button>
              )}
              <button className="text-gray-500 hover:text-red-600 flex items-center">
                <Flag className="w-4 h-4 mr-1" />
                Signaler
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4 space-y-4">
          {comment.replies.map(reply => renderComment(reply, true))}
        </div>
      )}
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto mt-12">
      <div className="bg-gray-50 rounded-xl p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <MessageCircle className="w-5 h-5 mr-2" />
          Commentaires ({comments.reduce((acc, comment) => acc + 1 + (comment.replies?.length || 0), 0)})
        </h3>

        {/* Formulaire de commentaire */}
        <form onSubmit={handleSubmitComment} className="mb-8">
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={replyingTo ? "Rédigez votre réponse..." : "Partagez votre opinion sur cet article..."}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              rows={4}
              required
            />
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-gray-500">
                Les commentaires sont modérés avant publication.
              </p>
              <div className="flex space-x-2">
                {replyingTo && (
                  <button
                    type="button"
                    onClick={() => {setReplyingTo(null); setNewComment('');}}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Annuler
                  </button>
                )}
                <button
                  type="submit"
                  className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center"
                >
                  <Send className="w-4 h-4 mr-1" />
                  Publier
                </button>
              </div>
            </div>
          </div>
        </form>

        {/* Liste des commentaires */}
        <div className="space-y-6">
          {comments.map(comment => renderComment(comment))}
        </div>
      </div>
    </div>
  );
};

export default CommentSection;