import React from 'react';
import { useParams } from 'react-router-dom';
import ArticleList from '../components/article/ArticleList';
import articlesData from '../data/articles.json';
import type { Article } from '../types';

const CategoryPage: React.FC = () => {
  const { categorySlug } = useParams<{ categorySlug: string }>();
  
  const articles: Article[] = articlesData.articles;
  const category = articlesData.categories.find(cat => cat.slug === categorySlug);
  
  if (!category) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Catégorie non trouvée</h1>
          <p className="text-gray-600">La catégorie demandée n'existe pas.</p>
        </div>
      </div>
    );
  }

  const categoryArticles = articles.filter(article => article.category === category.name);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      {/* En-tête de catégorie */}
      <div className="mb-12">
        <div className="text-center">
          <div 
            className="inline-flex items-center px-4 py-2 rounded-full text-white font-semibold mb-4"
            style={{ backgroundColor: category.color }}
          >
            {category.name}
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {category.name}
          </h1>
          <p className="text-xl text-gray-600">
            Découvrez tous nos articles sur {category.name.toLowerCase()}
          </p>
        </div>
      </div>

      {/* Articles de la catégorie */}
      <ArticleList articles={categoryArticles} />
    </div>
  );
};

export default CategoryPage;