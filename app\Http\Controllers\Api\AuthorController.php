<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Author;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Author::with('admin');

        // Recherche par nom ou email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nom', 'like', '%' . $search . '%')
                  ->orWhere('prenom', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%');
            });
        }

        $authors = $query->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => $authors->map(function ($author) {
                return [
                    'id' => $author->id,
                    'nom' => $author->nom,
                    'prenom' => $author->prenom,
                    'email' => $author->email,
                    'telephone' => $author->telephone,
                    'full_name' => $author->full_name,
                    'admin_id' => $author->admin_id,
                    'admin_name' => $author->admin ? $author->admin->full_name : null,
                    'created_at' => $author->created_at,
                ];
            }),
            'pagination' => [
                'current_page' => $authors->currentPage(),
                'last_page' => $authors->lastPage(),
                'per_page' => $authors->perPage(),
                'total' => $authors->total(),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required|string|max:50',
            'prenom' => 'required|string|max:50',
            'telephone' => 'nullable|string|max:20',
            'email' => 'required|email|unique:authors,email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $author = Author::create([
            'admin_id' => $request->user()->id, // L'admin connecté
            'nom' => $request->nom,
            'prenom' => $request->prenom,
            'telephone' => $request->telephone,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        $author->load('admin');

        return response()->json([
            'success' => true,
            'message' => 'Auteur créé avec succès',
            'data' => [
                'id' => $author->id,
                'nom' => $author->nom,
                'prenom' => $author->prenom,
                'email' => $author->email,
                'telephone' => $author->telephone,
                'full_name' => $author->full_name,
                'admin_id' => $author->admin_id,
                'created_at' => $author->created_at,
            ]
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $author = Author::with(['admin', 'articles'])->find($id);

        if (!$author) {
            return response()->json([
                'success' => false,
                'message' => 'Auteur non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $author->id,
                'nom' => $author->nom,
                'prenom' => $author->prenom,
                'email' => $author->email,
                'telephone' => $author->telephone,
                'full_name' => $author->full_name,
                'admin_id' => $author->admin_id,
                'admin_name' => $author->admin ? $author->admin->full_name : null,
                'articles_count' => $author->articles->count(),
                'created_at' => $author->created_at,
            ]
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $author = Author::find($id);

        if (!$author) {
            return response()->json([
                'success' => false,
                'message' => 'Auteur non trouvé'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'nom' => 'sometimes|required|string|max:50',
            'prenom' => 'sometimes|required|string|max:50',
            'telephone' => 'nullable|string|max:20',
            'email' => 'sometimes|required|email|unique:authors,email,' . $id,
            'password' => 'nullable|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Hasher le mot de passe si fourni
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $author->update($data);
        $author->load('admin');

        return response()->json([
            'success' => true,
            'message' => 'Auteur mis à jour avec succès',
            'data' => [
                'id' => $author->id,
                'nom' => $author->nom,
                'prenom' => $author->prenom,
                'email' => $author->email,
                'telephone' => $author->telephone,
                'full_name' => $author->full_name,
                'admin_id' => $author->admin_id,
                'created_at' => $author->created_at,
            ]
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $author = Author::find($id);

        if (!$author) {
            return response()->json([
                'success' => false,
                'message' => 'Auteur non trouvé'
            ], 404);
        }

        $author->delete();

        return response()->json([
            'success' => true,
            'message' => 'Auteur supprimé avec succès'
        ]);
    }
}
