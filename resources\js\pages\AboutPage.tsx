import React from 'react';
import { Trophy, Users, Target, Award, Mail, Phone } from 'lucide-react';

const AboutPage: React.FC = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Rédacteur en chef",
      bio: "Journaliste sportif depuis 15 ans, spécialisé dans l'analyse tactique et les grands événements internationaux.",
      image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg"
    },
    {
      name: "<PERSON>",
      role: "Corresponda<PERSON>",
      bio: "Experte du mercato européen, elle suit de près les mouvements des clubs et les négociations.",
      image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg"
    },
    {
      name: "<PERSON>",
      role: "Analyste Statistiques",
      bio: "Data analyst spécialisé dans les statistiques football, il décrypte les performances avec précision.",
      image: "https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg"
    }
  ];

  const values = [
    {
      icon: <Target className="w-8 h-8" />,
      title: "Excellence journalistique",
      description: "Nous nous engageons à fournir des informations précises, vérifiées et de haute qualité."
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Communauté passionnée",
      description: "Nous créons un espace d'échange pour tous les amoureux du football, amateur ou professionnel."
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "Innovation constante",
      description: "Nous utilisons les dernières technologies pour offrir une expérience de lecture optimale."
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 to-green-800 text-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Trophy className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">À propos de FootballZone</h1>
          <p className="text-xl text-green-100 max-w-2xl mx-auto">
            Le journal numérique de référence pour tous les passionnés de football, 
            avec des analyses expertes et une couverture complète de l'actualité mondiale.
          </p>
        </div>
      </section>

      {/* Mission */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Notre Mission</h2>
            <p className="text-lg text-gray-600 leading-relaxed">
              Depuis notre création, FootballZone s'est donné pour mission de démocratiser 
              l'accès à l'information football de qualité. Nous croyons que chaque passionné 
              mérite d'avoir accès aux meilleures analyses, aux résultats en temps réel et 
              aux coulisses du monde du football.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center p-6 rounded-xl bg-gray-50">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Équipe */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Notre Équipe</h2>
            <p className="text-lg text-gray-600">
              Une équipe de journalistes passionnés et d'experts du football
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm overflow-hidden">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-green-600 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Statistiques */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">FootballZone en chiffres</h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">500K+</div>
              <div className="text-gray-600">Lecteurs mensuels</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">2K+</div>
              <div className="text-gray-600">Articles publiés</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">50+</div>
              <div className="text-gray-600">Championnats couverts</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-gray-600">Actualités en temps réel</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Contactez-nous</h2>
            <p className="text-gray-300">
              Une question, une suggestion ou une proposition ? N'hésitez pas à nous contacter.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="flex items-center">
                <Mail className="w-6 h-6 text-green-400 mr-4" />
                <div>
                  <div className="font-semibold">Email</div>
                  <div className="text-gray-300"><EMAIL></div>
                </div>
              </div>
              <div className="flex items-center">
                <Phone className="w-6 h-6 text-green-400 mr-4" />
                <div>
                  <div className="font-semibold">Téléphone</div>
                  <div className="text-gray-300">+33 1 23 45 67 89</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-xl">
              <h3 className="text-lg font-semibold mb-4">Envoyez-nous un message</h3>
              <form className="space-y-4">
                <input
                  type="email"
                  placeholder="Votre email"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <textarea
                  placeholder="Votre message"
                  rows={4}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                />
                <button
                  type="submit"
                  className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Envoyer
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;