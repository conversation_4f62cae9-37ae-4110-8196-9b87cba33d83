<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Commentaire extends Model
{
    use HasFactory;

    protected $fillable = [
        'article_id',
        'user_id',
        'message',
        'date_commentaire',
        'heure_commentaire',
    ];

    protected $casts = [
        'date_commentaire' => 'date',
        'heure_commentaire' => 'datetime',
    ];

    /**
     * Relation avec l'article
     */
    public function article()
    {
        return $this->belongsTo(Article::class);
    }

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope pour les commentaires récents
     */
    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('date_commentaire', 'desc')
                    ->orderBy('heure_commentaire', 'desc')
                    ->limit($limit);
    }

    /**
     * Scope pour les commentaires d'un article
     */
    public function scopeForArticle($query, $articleId)
    {
        return $query->where('article_id', $articleId);
    }
}
