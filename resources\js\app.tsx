import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AdminProvider } from './contexts/AdminContext';
import Layout from './components/layout/Layout';
import AdminLayout from './components/admin/Layout/AdminLayout';
import ProtectedRoute from './components/admin/ProtectedRoute';
import HomePage from './pages/HomePage';
import CategoryPage from './pages/CategoryPage';
import ArticlePage from './pages/ArticlePage';
import SearchPage from './pages/SearchPage';
import NewsletterPage from './pages/NewsletterPage';
import AboutPage from './pages/AboutPage';
import LoginPage from './pages/admin/LoginPage';
import DashboardPage from './pages/admin/DashboardPage';
import ArticlesPage from './pages/admin/ArticlesPage';
import AddArticlePage from './pages/admin/AddArticlePage';
import EditArticlePage from './pages/admin/EditArticlePage';
import CategoriesPage from './pages/admin/CategoriesPage';
import MediasPage from './pages/admin/MediasPage';
import CommentsPage from './pages/admin/CommentsPage';
import SubscribersPage from './pages/admin/SubscribersPage';

function App() {
  return (
    <AdminProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="category/:categorySlug" element={<CategoryPage />} />
            <Route path="article/:articleSlug" element={<ArticlePage />} />
            <Route path="search" element={<SearchPage />} />
            <Route path="newsletter" element={<NewsletterPage />} />
            <Route path="about" element={<AboutPage />} />
            <Route path="contact" element={<AboutPage />} />
          </Route>
          <Route path="/admin/login" element={<LoginPage />} />
          <Route path="/admin" element={
            <ProtectedRoute>
              <AdminLayout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="articles" element={<ArticlesPage />} />
            <Route path="articles/add" element={<AddArticlePage />} />
            <Route path="articles/edit/:id" element={<EditArticlePage />} />
            <Route path="categories" element={<CategoriesPage />} />
            <Route path="medias" element={<MediasPage />} />
            <Route path="comments" element={<CommentsPage />} />
            <Route path="subscribers" element={<SubscribersPage />} />
          </Route>
        </Routes>
      </Router>
    </AdminProvider>
  );
}

export default App;