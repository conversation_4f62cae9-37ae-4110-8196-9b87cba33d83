<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Admin;
use App\Models\Author;
use App\Models\Article;
use App\Models\Commentaire;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Créer un admin
        $admin = Admin::create([
            'nom' => 'Admin',
            'prenom' => 'Super',
            'email' => '<EMAIL>',
            'telephone' => '0123456789',
            'password' => Hash::make('admin123'),
        ]);

        // Créer des auteurs
        $author1 = Author::create([
            'admin_id' => $admin->id,
            'nom' => 'Dubois',
            'prenom' => 'Antoine',
            'email' => '<EMAIL>',
            'telephone' => '0123456790',
            'password' => Hash::make('author123'),
        ]);

        $author2 = Author::create([
            'admin_id' => $admin->id,
            'nom' => '<PERSON>',
            'prenom' => 'Sophie',
            'email' => '<EMAIL>',
            'telephone' => '0123456791',
            'password' => Hash::make('author123'),
        ]);

        // Créer des utilisateurs
        $user1 = User::create([
            'nom' => 'Jean Dupont',
            'email' => '<EMAIL>',
            'password' => Hash::make('user123'),
        ]);

        $user2 = User::create([
            'nom' => 'Marie Durand',
            'email' => '<EMAIL>',
            'password' => Hash::make('user123'),
        ]);

        // Créer des articles
        $article1 = Article::create([
            'author_id' => $author1->id,
            'titre' => 'PSG vs Manchester City : Le Choc des Titans en Ligue des Champions',
            'message' => 'Le Parc des Princes s\'apprête à vivre une soirée européenne exceptionnelle ce soir. Le PSG, fort de son parcours impressionnant cette saison, reçoit Manchester City dans un contexte de haute tension. Les deux équipes, menées respectivement par Luis Enrique et Pep Guardiola, ont préparé cette rencontre avec le plus grand soin.',
            'image' => 'articles/psg-city.jpg',
            'date_publication' => now()->toDateString(),
            'heure_publication' => '18:30:00',
        ]);

        $article2 = Article::create([
            'author_id' => $author2->id,
            'titre' => 'Mercato : Kylian Mbappé vers le Real Madrid ?',
            'message' => 'Le feuilleton Kylian Mbappé reprend de plus belle. Selon nos informations, le Real Madrid aurait intensifié ses approches pour attirer l\'attaquant du PSG dans la capitale espagnole. Florentino Pérez, président du club merengue, aurait fait de Mbappé sa priorité absolue pour le prochain mercato estival.',
            'image' => 'articles/mbappe-real.jpg',
            'date_publication' => now()->subDay()->toDateString(),
            'heure_publication' => '10:15:00',
        ]);

        // Créer des commentaires
        Commentaire::create([
            'article_id' => $article1->id,
            'user_id' => $user1->id,
            'message' => 'Excellent article ! J\'ai hâte de voir ce match.',
            'date_commentaire' => now()->toDateString(),
            'heure_commentaire' => '19:00:00',
        ]);

        Commentaire::create([
            'article_id' => $article1->id,
            'user_id' => $user2->id,
            'message' => 'Le PSG va gagner, j\'en suis sûre !',
            'date_commentaire' => now()->toDateString(),
            'heure_commentaire' => '19:15:00',
        ]);

        Commentaire::create([
            'article_id' => $article2->id,
            'user_id' => $user1->id,
            'message' => 'Mbappé au Real, ça serait un transfert historique.',
            'date_commentaire' => now()->subDay()->toDateString(),
            'heure_commentaire' => '11:30:00',
        ]);
    }
}
