import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, User, Calendar, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { Article } from '../../types';

interface ArticleCardProps {
  article: Article;
  variant?: 'default' | 'featured' | 'compact';
}

const ArticleCard: React.FC<ArticleCardProps> = ({ article, variant = 'default' }) => {
  const formattedDate = format(new Date(article.date), 'dd MMMM yyyy', { locale: fr });

  if (variant === 'featured') {
    return (
      <Link to={`/article/${article.slug}`} className="block group">
        <div className="relative overflow-hidden rounded-2xl shadow-lg bg-white">
          <div className="aspect-w-16 aspect-h-9 relative">
            <img
              src={article.image}
              alt={article.title}
              className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <span className="inline-block px-3 py-1 bg-green-500 rounded-full text-xs font-medium mb-3">
                {article.category}
              </span>
              <h2 className="text-2xl font-bold mb-2 group-hover:text-green-400 transition-colors">
                {article.title}
              </h2>
              <p className="text-gray-200 mb-3 line-clamp-2">{article.excerpt}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-300">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  {article.author}
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {article.readTime} min
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  if (variant === 'compact') {
    return (
      <Link to={`/article/${article.slug}`} className="block group">
        <div className="flex space-x-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
          <img
            src={article.image}
            alt={article.title}
            className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
            loading="lazy"
          />
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 group-hover:text-green-600 transition-colors line-clamp-2">
              {article.title}
            </h3>
            <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
              <span>{formattedDate}</span>
              <span>•</span>
              <span>{article.readTime} min</span>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link to={`/article/${article.slug}`} className="block group">
      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
        <div className="relative">
          <img
            src={article.image}
            alt={article.title}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            loading="lazy"
          />
          <span 
            className="absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-semibold text-white"
            style={{ backgroundColor: '#10B981' }}
          >
            {article.category}
          </span>
        </div>
        
        <div className="p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors line-clamp-2">
            {article.title}
          </h3>
          <p className="text-gray-600 mb-4 line-clamp-3">{article.excerpt}</p>
          
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <User className="w-4 h-4 mr-1" />
                {article.author}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {article.readTime} min
              </div>
            </div>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              {formattedDate}
            </div>
          </div>
          
          {article.tags && article.tags.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {article.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </Link>
  );
};

export default ArticleCard;