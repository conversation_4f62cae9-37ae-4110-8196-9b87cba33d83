import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Search, Filter, X } from 'lucide-react';
import ArticleList from '../components/article/ArticleList';
import { useSearch } from '../hooks/useSearch';
import articlesData from '../data/articles.json';
import type { Article } from '../types';

const SearchPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialQuery = searchParams.get('q') || '';
  
  const articles: Article[] = articlesData.articles;
  const { 
    searchTerm, 
    setSearchTerm, 
    selectedCategory, 
    setSelectedCategory, 
    filteredArticles 
  } = useSearch(articles);

  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    setSearchTerm(initialQuery);
  }, [initialQuery, setSearchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setSearchParams({ q: searchTerm });
    } else {
      setSearchParams({});
    }
  };

  const clearFilters = () => {
    setSelectedCategory(null);
    setSearchTerm('');
    setSearchParams({});
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      {/* En-tête de recherche */}
      <div className="mb-12">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Recherche d'articles</h1>
        
        {/* Barre de recherche */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="relative max-w-2xl">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Rechercher des articles..."
              className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <Search className="absolute left-4 top-4 w-6 h-6 text-gray-400" />
            <button
              type="submit"
              className="absolute right-2 top-2 bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors"
            >
              Rechercher
            </button>
          </div>
        </form>

        {/* Filtres */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Filter className="w-4 h-4" />
            <span>Filtres</span>
          </button>

          {(searchTerm || selectedCategory) && (
            <button
              onClick={clearFilters}
              className="flex items-center space-x-2 px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Effacer</span>
            </button>
          )}
        </div>

        {/* Panel de filtres */}
        {showFilters && (
          <div className="mt-6 p-6 bg-gray-50 rounded-xl">
            <h3 className="font-semibold text-gray-900 mb-4">Filtrer par catégorie</h3>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  !selectedCategory 
                    ? 'bg-green-500 text-white' 
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Toutes
              </button>
              {articlesData.categories.map((category) => (
                <button
                  key={category.slug}
                  onClick={() => setSelectedCategory(category.name)}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.name
                      ? 'text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                  style={{
                    backgroundColor: selectedCategory === category.name ? category.color : undefined
                  }}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Résultats */}
      <div className="mb-8">
        {searchTerm || selectedCategory ? (
          <div className="flex items-center space-x-2 text-gray-600 mb-6">
            <span>
              {filteredArticles.length} résultat{filteredArticles.length > 1 ? 's' : ''} trouvé{filteredArticles.length > 1 ? 's' : ''}
            </span>
            {searchTerm && (
              <span>
                pour "<strong className="text-gray-900">{searchTerm}</strong>"
              </span>
            )}
            {selectedCategory && (
              <span>
                dans la catégorie "<strong className="text-gray-900">{selectedCategory}</strong>"
              </span>
            )}
          </div>
        ) : (
          <p className="text-gray-600 mb-6">
            Parcourez tous nos articles ou utilisez la recherche pour trouver un contenu spécifique.
          </p>
        )}
      </div>

      {/* Liste des articles */}
      <ArticleList articles={filteredArticles} />
    </div>
  );
};

export default SearchPage;