import React, { useState } from 'react';
import { Mail, Check } from 'lucide-react';

interface NewsletterProps {
  variant?: 'inline' | 'page';
}

const Newsletter: React.FC<NewsletterProps> = ({ variant = 'inline' }) => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simuler l'inscription
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail('');
    }, 1000);
  };

  if (variant === 'page') {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Restez informé avec notre newsletter
            </h1>
            <p className="text-gray-600 text-lg leading-relaxed">
              Recevez chaque jour les dernières actualités football, analyses exclusives 
              et résultats en temps réel directement dans votre boîte mail.
            </p>
          </div>

          {isSubscribed ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Merci pour votre inscription !</h3>
              <p className="text-gray-600">Vous allez recevoir un email de confirmation.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Adresse email
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-600 hover:to-green-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Inscription...' : "S'inscrire à la newsletter"}
              </button>
            </form>
          )}

          <div className="mt-8 p-6 bg-gray-50 rounded-xl">
            <h4 className="font-semibold text-gray-900 mb-3">Ce que vous recevrez :</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                Résumé quotidien de l'actualité football
              </li>
              <li className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                Analyses exclusives de nos experts
              </li>
              <li className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                Alertes transferts et mercato
              </li>
              <li className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                Statistiques et données avancées
              </li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
      <div className="flex items-center mb-4">
        <Mail className="w-6 h-6 mr-2" />
        <h3 className="text-lg font-semibold">Newsletter FootballZone</h3>
      </div>
      <p className="text-green-100 mb-4 text-sm">
        Recevez les dernières actualités football directement dans votre boîte mail.
      </p>
      
      {isSubscribed ? (
        <div className="flex items-center text-green-100">
          <Check className="w-5 h-5 mr-2" />
          <span className="text-sm">Merci pour votre inscription !</span>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="flex-1 px-3 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
            required
          />
          <button
            type="submit"
            disabled={isLoading}
            className="bg-white text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 whitespace-nowrap"
          >
            {isLoading ? '...' : "S'inscrire"}
          </button>
        </form>
      )}
    </div>
  );
};

export default Newsletter;