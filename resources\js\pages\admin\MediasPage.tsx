import React, { useState } from 'react';
import { PlusIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import adminData from '../../data/adminData.json';
import type { Media } from '../../types/admin';

const MediasPage: React.FC = () => {
  const [medias, setMedias] = useState<Media[]>(adminData.medias);
  const [selectedType, setSelectedType] = useState<string>('all');

  const handleDelete = (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce média ?')) {
      setMedias(prev => prev.filter(media => media.id !== id));
    }
  };

  const handleUpload = () => {
    // Simulation d'upload
    const newMedia: Media = {
      id: Date.now(),
      name: 'nouveau-media.jpg',
      type: 'image',
      size: '1.2 MB',
      url: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg',
      uploadDate: new Date().toISOString()
    };
    setMedias(prev => [newMedia, ...prev]);
  };

  const filteredMedias = medias.filter(media => 
    selectedType === 'all' || media.type === selectedType
  );

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return '🖼️';
      case 'video':
        return '🎥';
      case 'audio':
        return '🎵';
      default:
        return '📄';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-blue-100 text-blue-800';
      case 'video':
        return 'bg-red-100 text-red-800';
      case 'audio':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Médiathèque</h1>
          <p className="text-gray-600">Gérez vos images, vidéos et fichiers audio</p>
        </div>
        <button
          onClick={handleUpload}
          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Ajouter un média
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-gray-900">{medias.length}</div>
          <div className="text-sm text-gray-600">Total médias</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-blue-600">
            {medias.filter(m => m.type === 'image').length}
          </div>
          <div className="text-sm text-gray-600">Images</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-red-600">
            {medias.filter(m => m.type === 'video').length}
          </div>
          <div className="text-sm text-gray-600">Vidéos</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="text-2xl font-bold text-green-600">
            {medias.filter(m => m.type === 'audio').length}
          </div>
          <div className="text-sm text-gray-600">Audio</div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Filtrer par type :</label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="all">Tous les types</option>
            <option value="image">Images</option>
            <option value="video">Vidéos</option>
            <option value="audio">Audio</option>
          </select>
        </div>
      </div>

      {/* Media Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredMedias.map((media) => (
          <div key={media.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Media Preview */}
            <div className="aspect-w-16 aspect-h-9 bg-gray-100">
              {media.type === 'image' ? (
                <img
                  src={media.url}
                  alt={media.name}
                  className="w-full h-48 object-cover"
                />
              ) : (
                <div className="w-full h-48 flex items-center justify-center text-4xl">
                  {getTypeIcon(media.type)}
                </div>
              )}
            </div>

            {/* Media Info */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {media.name}
                </h3>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(media.type)}`}>
                  {media.type}
                </span>
              </div>
              
              <div className="text-xs text-gray-500 mb-3">
                <p>Taille: {media.size}</p>
                <p>Ajouté le {format(new Date(media.uploadDate), 'dd/MM/yyyy', { locale: fr })}</p>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <button
                  onClick={() => window.open(media.url, '_blank')}
                  className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                >
                  <EyeIcon className="w-4 h-4 mr-1" />
                  Voir
                </button>
                <button
                  onClick={() => handleDelete(media.id)}
                  className="flex items-center text-red-600 hover:text-red-800 text-sm"
                >
                  <TrashIcon className="w-4 h-4 mr-1" />
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredMedias.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <p className="text-gray-500">Aucun média trouvé pour ce filtre.</p>
        </div>
      )}
    </div>
  );
};

export default MediasPage;