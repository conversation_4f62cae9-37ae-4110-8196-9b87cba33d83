{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "vite": "^7.0.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "date-fns": "^4.1.0", "lucide-react": "^0.540.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-quill-new": "^3.6.0", "react-router-dom": "^7.8.1", "recharts": "^3.1.2"}}